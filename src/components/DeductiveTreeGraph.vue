<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, computed, nextTick } from 'vue'
import * as d3 from 'd3'
import { AnalysisNode, NodeConnection } from '../utils/systemicThinking'

interface NetworkNode extends AnalysisNode {
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
  vx?: number;
  vy?: number;
  index?: number;
}

interface NetworkLink extends NodeConnection {
  source: NetworkNode | string | number;
  target: NetworkNode | string | number;
  index?: number;
}

interface Props {
  nodes: AnalysisNode[];
  connections: NodeConnection[];
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'nodes-updated': [nodes: AnalysisNode[]]
  'connections-updated': [connections: NodeConnection[]]
}>()

const svgRef = ref<SVGElement | null>(null)
const currentStep = ref(0)
const history = ref<{ nodes: NetworkNode[], links: NetworkLink[] }[]>([])
const maxSteps = ref(0)
const editingNode = ref<string | null>(null)
const editingText = ref('')
const hoveredNode = ref<string | null>(null)
const selectedNode = ref<string | null>(null)
const showNodeActions = ref(false)
const nodeActionPosition = ref({ x: 0, y: 0 })
const zoomBehavior = ref<any>(null)
const currentZoom = ref(1)
const isGenerating = ref(false)
const renderTimeout = ref<number | null>(null)
const showNodeList = ref(true)

const width = 1200
const height = 800

// 文本换行工具函数
const wrapText = (text: string, maxLength: number): string[] => {
  if (text.length <= maxLength) return [text]

  const lines: string[] = []
  let currentLine = ''

  for (let i = 0; i < text.length; i++) {
    const char = text[i]
    if (currentLine.length + 1 <= maxLength) {
      currentLine += char
    } else {
      if (currentLine) lines.push(currentLine)
      currentLine = char
    }
  }

  if (currentLine) lines.push(currentLine)
  return lines
}

// 防抖渲染函数
const debouncedRender = (networkData: { nodes: NetworkNode[], links: NetworkLink[] }) => {
  if (renderTimeout.value) {
    clearTimeout(renderTimeout.value)
  }
  renderTimeout.value = window.setTimeout(() => {
    renderNetwork(networkData)
  }, 100)
}

// 拖拽事件处理已在渲染函数中内联定义

// 节点交互事件处理
const handleNodeMouseOver = (event: any, d: NetworkNode) => {
  hoveredNode.value = d.id
  // 获取SVG相对位置
  const svgRect = svgRef.value!.getBoundingClientRect()
  const nodeRect = (event.target as Element).getBoundingClientRect()
  nodeActionPosition.value = {
    x: nodeRect.left - svgRect.left + nodeRect.width / 2,
    y: nodeRect.top - svgRect.top + nodeRect.height + 10
  }

  // 延迟显示菜单，避免快速移动时闪烁
  setTimeout(() => {
    if (hoveredNode.value === d.id) {
      showNodeActions.value = true
    }
  }, 300)
}

const handleNodeMouseOut = (event: any, d: NetworkNode) => {
  // 检查鼠标是否移动到菜单上
  const relatedTarget = event.relatedTarget
  if (relatedTarget && relatedTarget.closest('.node-actions-menu')) {
    return // 不隐藏菜单
  }

  setTimeout(() => {
    if (hoveredNode.value === d.id && !showNodeActions.value) {
      hoveredNode.value = null
    }
  }, 200)
}

const handleNodeDoubleClick = (event: any, d: NetworkNode) => {
  event.stopPropagation()
  showNodeActions.value = false
  editingNode.value = d.id
  editingText.value = d.title
}

const handleNodeClick = (event: any, d: NetworkNode) => {
  event.stopPropagation()
  // 设置选中节点
  selectedNode.value = d.id

  if (hoveredNode.value === d.id && !showNodeActions.value) {
    showNodeActions.value = true
  }
}

// 菜单交互处理
const handleMenuMouseEnter = () => {
  // 保持菜单显示
}

const handleMenuMouseLeave = () => {
  showNodeActions.value = false
  hoveredNode.value = null
}

// 缩放控制函数
const zoomIn = () => {
  if (svgRef.value && zoomBehavior.value) {
    d3.select(svgRef.value)
      .transition()
      .duration(300)
      .call(zoomBehavior.value.scaleBy, 1.5)
  }
}

const zoomOut = () => {
  if (svgRef.value && zoomBehavior.value) {
    d3.select(svgRef.value)
      .transition()
      .duration(300)
      .call(zoomBehavior.value.scaleBy, 1 / 1.5)
  }
}

const resetZoom = () => {
  if (svgRef.value && zoomBehavior.value) {
    d3.select(svgRef.value)
      .transition()
      .duration(500)
      .call(zoomBehavior.value.transform, d3.zoomIdentity)
  }
}

// 神经网络样式的力导向图渲染
const renderNetwork = (networkData: { nodes: NetworkNode[], links: NetworkLink[] }) => {
  console.log('DeductiveTreeGraph: Rendering network with data:', networkData);
  if (!svgRef.value || !networkData.nodes.length) {
    console.warn('DeductiveTreeGraph: Cannot render - missing svgRef or nodes');
    return
  }

  try {
    d3.select(svgRef.value).selectAll('*').remove()

    const svg = d3.select(svgRef.value)
      .attr("width", width)
      .attr("height", height)

    // 添加缩放和平移功能
    const zoom = d3.zoom()
      .scaleExtent([0.1, 4])
      .on("zoom", (event: any) => {
        container.attr("transform", event.transform)
        currentZoom.value = event.transform.k
      })

    svg.call(zoom as any)
    zoomBehavior.value = zoom

    // 创建容器组
    const container = svg.append("g")
      .attr("class", "zoom-container")

    // 创建力导向仿真
    const simulation = d3.forceSimulation(networkData.nodes)
      .force("link", d3.forceLink(networkData.links).id((d: any) => d.id).distance(120).strength(0.5))
      .force("charge", d3.forceManyBody().strength(-400))
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force("collision", d3.forceCollide().radius((d: any) => {
        // 根据节点大小设置碰撞半径
        const title = d.title || d.content || '节点'
        const baseSize = d.type === 'initial' ? 25 : 20
        const textLength = title.length
        const sizeAdjustment = Math.min(textLength * 0.8, 8)
        const nodeRadius = Math.max(baseSize + sizeAdjustment, d.type === 'initial' ? 25 : 20)
        return nodeRadius + 5 // 添加一些间距
      }))

    // 创建连接线
    const links = container.append("g")
      .attr("class", "links")
      .selectAll("line")
      .data(networkData.links)
      .enter().append("line")
      .attr("stroke", (d: any) => {
        const sourceType = (d.source as NetworkNode).type
        const targetType = (d.target as NetworkNode).type
        if (sourceType === 'positive' || targetType === 'positive') return '#22c55e'
        if (sourceType === 'negative' || targetType === 'negative') return '#ef4444'
        return '#94a3b8'
      })
      .attr("stroke-width", (d: any) => Math.max(1, d.strength * 3))
      .attr("stroke-opacity", 0.6)

    // 创建节点组
    const nodeGroups = container.append("g")
      .attr("class", "nodes")
      .selectAll("g")
      .data(networkData.nodes)
      .enter().append("g")
      .attr("class", "node-group")
      .style("cursor", "grab")
      .call(d3.drag<any, NetworkNode>()
        .on("start", (event: any, d: NetworkNode) => {
          if (!event.active) simulation.alphaTarget(0.3).restart()
          d.fx = d.x
          d.fy = d.y
          d3.select(event.currentTarget).style("cursor", "grabbing")
        })
        .on("drag", (event: any, d: NetworkNode) => {
          d.fx = event.x
          d.fy = event.y
        })
        .on("end", (event: any, d: NetworkNode) => {
          if (!event.active) simulation.alphaTarget(0)
          d.fx = null
          d.fy = null
          d3.select(event.currentTarget).style("cursor", "grab")
        }))

    // 节点背景圆圈（恢复简洁大小）
    nodeGroups.append("circle")
      .attr("r", (d: any) => {
        // 根据节点类型调整大小
        const baseSize = d.type === 'initial' ? 25 : 20
        return baseSize
      })
      .attr("fill", (d: any) => {
        if (d.type === 'initial') return '#6366f1'
        if (d.type === 'positive') return '#10b981'
        if (d.type === 'negative') return '#ef4444'
        return '#64748b'
      })
      .attr("stroke", (d: any) => {
        // 选中节点显示特殊边框
        return selectedNode.value === d.id ? '#fbbf24' : '#fff'
      })
      .attr("stroke-width", (d: any) => {
        // 选中节点边框更粗
        return selectedNode.value === d.id ? 3 : 1.5
      })
      .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))")

    // 节点文本（显示简短版本）
    nodeGroups.append("text")
      .attr("dy", ".35em")
      .attr("text-anchor", "middle")
      .style("font-size", "10px")
      .style("font-weight", "600")
      .style("fill", "#fff")
      .style("pointer-events", "none")
      .text((d: any) => {
        // 显示节点标题的简短版本
        const title = d.title || '节点'
        const maxLength = d.type === 'initial' ? 4 : 6
        return title.length > maxLength ? title.substring(0, maxLength) + '...' : title
      })

    // 节点完整信息提示（悬停时显示）
    nodeGroups.append("title")
      .text((d: any) => `${d.title}\n\n${d.description || '暂无描述'}`)

    // 添加节点交互事件
    nodeGroups
      .on("mouseover", handleNodeMouseOver)
      .on("mouseout", handleNodeMouseOut)
      .on("dblclick", handleNodeDoubleClick)
      .on("click", handleNodeClick)

    // 仿真更新函数
    simulation.on("tick", () => {
      links
        .attr("x1", (d: any) => d.source.x)
        .attr("y1", (d: any) => d.source.y)
        .attr("x2", (d: any) => d.target.x)
        .attr("y2", (d: any) => d.target.y)

      nodeGroups
        .attr("transform", (d: any) => `translate(${d.x},${d.y})`)
    })

    console.log('DeductiveTreeGraph: Network rendered with', networkData.nodes.length, 'nodes and', networkData.links.length, 'links');
  } catch (error) {
    console.error('DeductiveTreeGraph: Error rendering network:', error);
  }
}

// 将数据转换为网络格式
const buildNetwork = (nodes: AnalysisNode[], connections: NodeConnection[]): { nodes: NetworkNode[], links: NetworkLink[] } => {
  console.log('DeductiveTreeGraph: Building network with nodes:', nodes, 'and connections:', connections);

  // 转换节点，使用更好的初始位置分布
  const networkNodes: NetworkNode[] = nodes.map((node) => {
    // 为不同类型的节点设置不同的初始位置
    let x, y
    if (node.type === 'initial') {
      x = width / 2
      y = height / 2
    } else if (node.type === 'positive') {
      x = width / 2 + (Math.random() - 0.5) * 300
      y = height / 2 - 100 + (Math.random() - 0.5) * 200
    } else if (node.type === 'negative') {
      x = width / 2 + (Math.random() - 0.5) * 300
      y = height / 2 + 100 + (Math.random() - 0.5) * 200
    } else {
      x = width / 2 + (Math.random() - 0.5) * 400
      y = height / 2 + (Math.random() - 0.5) * 400
    }

    return {
      ...node,
      x,
      y
    }
  })

  // 转换连接
  const networkLinks: NetworkLink[] = connections.map(conn => ({
    ...conn,
    source: conn.from,
    target: conn.to
  }))

  return { nodes: networkNodes, links: networkLinks }
}

// 使用LLM生成更多节点的函数
const generateAdditionalNodes = async (baseNode: NetworkNode, direction: 'positive' | 'negative'): Promise<{ nodes: NetworkNode[], links: NetworkLink[] }> => {
  console.log(`🤖 开始LLM生成: ${direction}方向，基于节点"${baseNode.title}"`)

  try {
    // 获取当前所有节点的上下文
    const currentData = history.value[currentStep.value]
    const contextNodes = currentData.nodes.map(n => `${n.title}: ${n.description}`).join('\n')

    // 构建LLM提示
    const prompt = `基于现有的推演网络，为节点"${baseNode.title}"生成${direction === 'positive' ? '积极' : '负面'}方向的扩展推演。

当前推演网络包含以下节点：
${contextNodes}

请为"${baseNode.title}"生成3个${direction === 'positive' ? '积极' : '负面'}方向的子节点，要求：
1. 与现有节点相关但不重复
2. 具有逻辑性和深度
3. 符合${direction === 'positive' ? '积极' : '负面'}推演的方向

请严格按照以下JSON格式返回：
{
  "nodes": [
    {
      "title": "节点标题",
      "description": "节点描述",
      "impact": "low|medium|high|critical"
    }
  ]
}

只返回JSON，不要其他内容。`

    console.log('📤 发送LLM请求，提示长度:', prompt.length)

    // 调用LLM API
    const { LLMDeductiveAnalyzer } = await import('../utils/deductiveThinking')
    const analyzer = new LLMDeductiveAnalyzer()

    // 详细检查模型配置
    const modelConfig = analyzer.getModelConfig()
    console.log('🔍 检查模型配置:', modelConfig)

    if (!modelConfig) {
      console.warn('⚠️ 没有找到激活的模型配置，使用本地生成')
      throw new Error('NO_ACTIVE_MODEL')
    }

    if (!analyzer.hasValidConfig()) {
      console.warn('⚠️ 激活的模型没有配置API密钥，使用本地生成')
      console.log('📋 当前模型配置:', modelConfig)
      throw new Error('NO_API_KEY')
    }

    console.log('✅ 模型配置验证通过，开始调用LLM API')
    console.log('🔑 使用模型:', modelConfig.name, '提供商:', modelConfig.provider)

    const response = await analyzer.callLLMForExpansion(prompt)
    console.log('📥 LLM原始响应:', response.text)

    // 解析响应
    let parsedResponse
    try {
      // 清理响应文本
      const cleanText = response.text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim()
      console.log('🧹 清理后的响应:', cleanText)
      parsedResponse = JSON.parse(cleanText)
      console.log('✅ JSON解析成功:', parsedResponse)
    } catch (parseError) {
      console.error('❌ JSON解析失败:', parseError, '原始文本:', response.text)
      throw new Error('JSON_PARSE_ERROR')
    }

    if (!parsedResponse.nodes || !Array.isArray(parsedResponse.nodes)) {
      console.error('❌ 数据结构验证失败:', parsedResponse)
      throw new Error('INVALID_DATA_STRUCTURE')
    }

    if (parsedResponse.nodes.length === 0) {
      console.warn('⚠️ LLM返回了空节点数组')
      throw new Error('EMPTY_NODES')
    }

    const newNodes: NetworkNode[] = []
    const newLinks: NetworkLink[] = []

    // 创建新节点
    parsedResponse.nodes.forEach((nodeData: any, index: number) => {
      const nodeId = `${direction}-${baseNode.id}-${Date.now()}-${index}`
      const newNode: NetworkNode = {
        id: nodeId,
        title: nodeData.title || `${direction}推演${index + 1}`,
        description: nodeData.description || `基于"${baseNode.title}"的推演`,
        level: baseNode.level + 1,
        impact: nodeData.impact || 'medium',
        modelSource: `deductive-${direction}-llm`,
        type: direction,
        x: baseNode.x! + (Math.random() - 0.5) * 200,
        y: baseNode.y! + (Math.random() - 0.5) * 200
      }

      newNodes.push(newNode)

      // 创建连接
      newLinks.push({
        from: baseNode.id,
        to: nodeId,
        type: 'strong',
        description: `LLM生成的${direction}推演连接`,
        strength: 0.8,
        source: baseNode.id,
        target: nodeId
      })
    })

    console.log(`🎉 LLM生成成功: 创建了${newNodes.length}个节点`)
    return { nodes: newNodes, links: newLinks }

  } catch (error: any) {
    const errorType = error.message || 'UNKNOWN_ERROR'
    console.error(`❌ LLM生成失败 (${errorType}):`, error)

    // 根据错误类型提供不同的回退策略
    if (errorType === 'NO_API_KEY') {
      console.log('🔄 回退策略: 使用本地生成 (原因: 无API密钥)')
    } else if (errorType === 'JSON_PARSE_ERROR') {
      console.log('🔄 回退策略: 使用本地生成 (原因: JSON解析失败)')
    } else {
      console.log('🔄 回退策略: 使用本地生成 (原因: 其他错误)')
    }

    // 回退到本地生成
    const newNodes: NetworkNode[] = []
    const newLinks: NetworkLink[] = []

    for (let i = 0; i < 2; i++) {
      const nodeId = `${direction}-${baseNode.id}-${Date.now()}-${i}`
      const newNode: NetworkNode = {
        id: nodeId,
        title: `${baseNode.title}的${direction === 'positive' ? '积极' : '负面'}影响${i + 1}`,
        description: `基于"${baseNode.title}"的${direction === 'positive' ? '积极' : '负面'}方向推演`,
        level: baseNode.level + 1,
        impact: 'medium',
        modelSource: `deductive-${direction}-local`,
        type: direction,
        x: baseNode.x! + (Math.random() - 0.5) * 200,
        y: baseNode.y! + (Math.random() - 0.5) * 200
      }

      newNodes.push(newNode)

      newLinks.push({
        from: baseNode.id,
        to: nodeId,
        type: 'strong',
        description: `本地生成的${direction}推演连接`,
        strength: 0.7,
        source: baseNode.id,
        target: nodeId
      })
    }

    console.log(`🔧 本地生成完成: 创建了${newNodes.length}个节点`)
    return { nodes: newNodes, links: newLinks }
  }
}

// 节点生成历史记录接口
interface NodeGenerationStep {
  timestamp: number
  action: 'initial' | 'generate-positive' | 'generate-negative' | 'edit'
  baseNodeId?: string
  newNodeIds: string[]
  newLinkIds: string[]
}

// 节点生成历史
const generationHistory = ref<NodeGenerationStep[]>([])

// 创建时间线步骤 - 改进版本，支持动态节点生成历史
const createNetworkTimelineSteps = (networkData: { nodes: NetworkNode[], links: NetworkLink[] }): { nodes: NetworkNode[], links: NetworkLink[] }[] => {
  const steps: { nodes: NetworkNode[], links: NetworkLink[] }[] = []
  const { nodes, links } = networkData

  if (nodes.length === 0) {
    return steps
  }

  // 如果有生成历史，按历史重建时间线
  if (generationHistory.value.length > 0) {
    return createTimelineFromHistory(nodes, links)
  }

  // 否则使用默认的层级展示逻辑
  return createDefaultTimeline(nodes, links)
}

// 根据生成历史创建时间线
const createTimelineFromHistory = (allNodes: NetworkNode[], allLinks: NetworkLink[]): { nodes: NetworkNode[], links: NetworkLink[] }[] => {
  const steps: { nodes: NetworkNode[], links: NetworkLink[] }[] = []
  let currentNodes: NetworkNode[] = []
  let currentLinks: NetworkLink[] = []

  // 按时间顺序重放节点生成历史
  for (const historyStep of generationHistory.value) {
    // 添加新生成的节点
    const newNodes = allNodes.filter(n => historyStep.newNodeIds.includes(n.id))
    currentNodes.push(...newNodes)

    // 添加相关的连接
    const newLinks = allLinks.filter(l => historyStep.newLinkIds.includes(`${l.from}-${l.to}`))
    currentLinks.push(...newLinks)

    // 创建当前步骤的快照
    steps.push({
      nodes: [...currentNodes],
      links: [...currentLinks]
    })
  }

  return steps
}

// 默认时间线创建逻辑（按层级展示）
const createDefaultTimeline = (nodes: NetworkNode[], links: NetworkLink[]): { nodes: NetworkNode[], links: NetworkLink[] }[] => {
  const steps: { nodes: NetworkNode[], links: NetworkLink[] }[] = []

  // 步骤1: 只显示初始节点
  const initialNodes = nodes.filter(n => n.type === 'initial')
  if (initialNodes.length > 0) {
    steps.push({ nodes: [...initialNodes], links: [] })
  }

  // 步骤2: 显示初始节点和第一层节点
  const firstLevelNodes = nodes.filter(n => n.level <= 1)
  if (firstLevelNodes.length > initialNodes.length) {
    const firstLevelLinks = links.filter(l => {
      const sourceNode = nodes.find(n => n.id === l.from)
      const targetNode = nodes.find(n => n.id === l.to)
      return sourceNode && targetNode && sourceNode.level <= 1 && targetNode.level <= 1
    })
    steps.push({ nodes: [...firstLevelNodes], links: [...firstLevelLinks] })
  }

  // 步骤3: 显示所有节点
  steps.push({ nodes: [...nodes], links: [...links] })

  return steps
}

// 记录节点生成历史
const recordNodeGeneration = (action: NodeGenerationStep['action'], baseNodeId: string | undefined, newNodeIds: string[], newLinkIds: string[]) => {
  const step: NodeGenerationStep = {
    timestamp: Date.now(),
    action,
    baseNodeId,
    newNodeIds,
    newLinkIds
  }
  generationHistory.value.push(step)
  console.log('🕒 记录节点生成历史:', step)
}

// 获取步骤类型
const getStepType = (stepIndex: number): string | null => {
  if (stepIndex < generationHistory.value.length) {
    return generationHistory.value[stepIndex].action
  }
  return null
}

// 获取步骤描述
const getStepDescription = (stepIndex: number): string => {
  if (stepIndex < generationHistory.value.length) {
    const step = generationHistory.value[stepIndex]
    switch (step.action) {
      case 'initial':
        return '初始节点'
      case 'generate-positive':
        return `积极推演 (基于: ${step.baseNodeId})`
      case 'generate-negative':
        return `消极推演 (基于: ${step.baseNodeId})`
      case 'edit':
        return '节点编辑'
      default:
        return '未知操作'
    }
  }
  return `步骤 ${stepIndex + 1}`
}

// 获取当前步骤信息
const getCurrentStepInfo = (): string => {
  if (currentStep.value < history.value.length) {
    const stepData = history.value[currentStep.value]
    return `${stepData.nodes.length} 个节点, ${stepData.links.length} 个连接`
  }
  return ''
}

// 获取当前步骤的节点列表
const getCurrentNodes = computed(() => {
  if (currentStep.value < history.value.length) {
    return history.value[currentStep.value].nodes
  }
  return []
})

// 从列表选择节点
const selectNodeFromList = (nodeId: string) => {
  selectedNode.value = nodeId
  // 可选：将节点居中显示
  centerNodeInView(nodeId)
}

// 将节点居中显示
const centerNodeInView = (nodeId: string) => {
  if (!svgRef.value || !zoomBehavior.value) return

  const currentData = history.value[currentStep.value]
  const node = currentData.nodes.find(n => n.id === nodeId)
  if (!node || !node.x || !node.y) return

  const svg = d3.select(svgRef.value)
  const transform = d3.zoomIdentity
    .translate(width / 2 - node.x, height / 2 - node.y)
    .scale(1.5)

  svg.transition()
    .duration(750)
    .call(zoomBehavior.value.transform, transform)
}

// 切换节点列表显示
const toggleNodeList = () => {
  showNodeList.value = !showNodeList.value
}









// Watch for props changes and update the visualization
watch([() => props.nodes, () => props.connections], ([newNodes, newConnections]) => {
  if (newNodes.length > 0) {
    const networkData = buildNetwork(newNodes, newConnections)
    if (networkData.nodes.length > 0) {
      // 重置生成历史并记录初始节点
      generationHistory.value = []
      const initialNodeIds = networkData.nodes.filter(n => n.type === 'initial').map(n => n.id)
      const initialLinkIds = networkData.links.map(l => `${l.from}-${l.to}`)

      if (initialNodeIds.length > 0) {
        recordNodeGeneration('initial', undefined, initialNodeIds, initialLinkIds)
      }

      history.value = createNetworkTimelineSteps(networkData)
      maxSteps.value = history.value.length
      currentStep.value = 0
      nextTick(() => {
        renderNetwork(history.value[currentStep.value])
      })
    }
  }
}, { immediate: true, deep: true })

// Navigation functions
const canGoBack = computed(() => currentStep.value > 0)
const canGoForward = computed(() => currentStep.value < history.value.length - 1)

const navigateHistory = (step: number) => {
  if (step >= 0 && step < history.value.length) {
    currentStep.value = step
    const networkData = history.value[currentStep.value]
    if (networkData) {
      nextTick(() => {
        renderNetwork(networkData)
      })
    }
  }
}

const goBack = () => {
  if (canGoBack.value) {
    navigateHistory(currentStep.value - 1)
  }
}

const goForward = () => {
  if (canGoForward.value) {
    navigateHistory(currentStep.value + 1)
  }
}

// Node editing functions
const saveNodeEdit = () => {
  if (editingNode.value && editingText.value.trim()) {
    const currentData = history.value[currentStep.value]
    const nodeToUpdate = currentData.nodes.find(n => n.id === editingNode.value)
    if (nodeToUpdate) {
      const oldTitle = nodeToUpdate.title
      nodeToUpdate.title = editingText.value.trim()

      // 记录编辑历史（如果标题确实发生了变化）
      if (oldTitle !== nodeToUpdate.title) {
        recordNodeGeneration('edit', nodeToUpdate.id, [nodeToUpdate.id], [])

        // 更新时间线以反映编辑
        const allNodes = [...currentData.nodes]
        const allLinks = [...currentData.links]
        const networkData = { nodes: allNodes, links: allLinks }
        history.value = createNetworkTimelineSteps(networkData)
        maxSteps.value = history.value.length
      }

      nextTick(() => {
        renderNetwork(currentData)
      })
      emit('nodes-updated', currentData.nodes)
    }
  }
  editingNode.value = null
  editingText.value = ''
}

const cancelNodeEdit = () => {
  editingNode.value = null
  editingText.value = ''
}

// Node generation functions
const generatePositiveNodes = async (baseNode: NetworkNode) => {
  if (isGenerating.value) return

  try {
    isGenerating.value = true
    showNodeActions.value = false

    const newData = await generateAdditionalNodes(baseNode, 'positive')

    // 记录节点生成历史
    const newNodeIds = newData.nodes.map(n => n.id)
    const newLinkIds = newData.links.map(l => `${l.from}-${l.to}`)
    recordNodeGeneration('generate-positive', baseNode.id, newNodeIds, newLinkIds)

    // 更新当前数据
    const currentData = history.value[currentStep.value]
    currentData.nodes.push(...newData.nodes)
    currentData.links.push(...newData.links)

    // 重新创建时间线步骤以包含新的生成历史
    const allNodes = [...currentData.nodes]
    const allLinks = [...currentData.links]
    const networkData = { nodes: allNodes, links: allLinks }
    history.value = createNetworkTimelineSteps(networkData)
    maxSteps.value = history.value.length

    // 跳转到最新步骤
    currentStep.value = history.value.length - 1

    nextTick(() => {
      renderNetwork(history.value[currentStep.value])
    })
    emit('nodes-updated', allNodes)
    emit('connections-updated', allLinks)
  } catch (error) {
    console.error('Failed to generate positive nodes:', error)
  } finally {
    isGenerating.value = false
  }
}

const generateNegativeNodes = async (baseNode: NetworkNode) => {
  if (isGenerating.value) return

  try {
    isGenerating.value = true
    showNodeActions.value = false

    const newData = await generateAdditionalNodes(baseNode, 'negative')

    // 记录节点生成历史
    const newNodeIds = newData.nodes.map(n => n.id)
    const newLinkIds = newData.links.map(l => `${l.from}-${l.to}`)
    recordNodeGeneration('generate-negative', baseNode.id, newNodeIds, newLinkIds)

    // 更新当前数据
    const currentData = history.value[currentStep.value]
    currentData.nodes.push(...newData.nodes)
    currentData.links.push(...newData.links)

    // 重新创建时间线步骤以包含新的生成历史
    const allNodes = [...currentData.nodes]
    const allLinks = [...currentData.links]
    const networkData = { nodes: allNodes, links: allLinks }
    history.value = createNetworkTimelineSteps(networkData)
    maxSteps.value = history.value.length

    // 跳转到最新步骤
    currentStep.value = history.value.length - 1

    nextTick(() => {
      renderNetwork(history.value[currentStep.value])
    })
    emit('nodes-updated', allNodes)
    emit('connections-updated', allLinks)
  } catch (error) {
    console.error('Failed to generate negative nodes:', error)
  } finally {
    isGenerating.value = false
  }
}

// Lifecycle hooks
onMounted(() => {
  if (props.nodes.length > 0) {
    const networkData = buildNetwork(props.nodes, props.connections)
    if (networkData.nodes.length > 0) {
      // 重置生成历史并记录初始节点
      generationHistory.value = []
      const initialNodeIds = networkData.nodes.filter(n => n.type === 'initial').map(n => n.id)
      const initialLinkIds = networkData.links.map(l => `${l.from}-${l.to}`)

      if (initialNodeIds.length > 0) {
        recordNodeGeneration('initial', undefined, initialNodeIds, initialLinkIds)
      }

      history.value = createNetworkTimelineSteps(networkData)
      maxSteps.value = history.value.length
      currentStep.value = 0
      nextTick(() => {
        renderNetwork(history.value[currentStep.value])
      })
    }
  }
})

onUnmounted(() => {
  if (renderTimeout.value) {
    clearTimeout(renderTimeout.value)
  }
  if (svgRef.value) {
    d3.select(svgRef.value).selectAll('*').remove()
  }
  showNodeActions.value = false
  editingNode.value = null
  hoveredNode.value = null
  isGenerating.value = false
})
</script>

<template>
  <div class="deductive-tree-graph relative w-full h-full flex">
    <!-- 主图区域 -->
    <div class="flex-1 relative" :class="{ 'pr-80': showNodeList }">
      <svg v-if="history.length > 0 && history[currentStep]" ref="svgRef" class="w-full h-full"></svg>

    <!-- 简化的节点编辑弹窗 -->
    <div v-if="editingNode" class="absolute inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50">
      <div class="bg-white dark:bg-slate-800 rounded-lg p-4 shadow-xl max-w-sm w-full mx-4">
        <input
          v-model="editingText"
          type="text"
          class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-800 dark:text-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          placeholder="输入节点标题"
          @keyup.enter="saveNodeEdit"
          @keyup.escape="cancelNodeEdit"
          autofocus
        />
        <div class="flex justify-end space-x-2 mt-3">
          <button
            @click="cancelNodeEdit"
            class="px-3 py-1 text-xs text-slate-600 hover:text-slate-800 rounded"
          >
            取消
          </button>
          <button
            @click="saveNodeEdit"
            class="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
          >
            保存
          </button>
        </div>
      </div>
    </div>

    <!-- 简化的节点操作菜单 -->
    <div
      v-if="showNodeActions && hoveredNode"
      class="node-actions-menu absolute z-50 bg-white dark:bg-slate-800 rounded-md shadow-lg border border-slate-200 dark:border-slate-700 p-1"
      :style="{ left: nodeActionPosition.x + 'px', top: nodeActionPosition.y + 'px' }"
      @mouseenter="handleMenuMouseEnter"
      @mouseleave="handleMenuMouseLeave"
    >
      <button
        @click="generatePositiveNodes(history[currentStep].nodes.find(n => n.id === hoveredNode)!)"
        :disabled="isGenerating"
        class="block w-full text-left px-2 py-1 text-xs text-green-600 hover:bg-green-50 rounded disabled:opacity-50 disabled:cursor-not-allowed"
        title="生成积极方向"
      >
        <span v-if="isGenerating" class="inline-block w-2 h-2 border border-green-600 border-t-transparent rounded-full animate-spin mr-1"></span>
        ➕ 积极
      </button>
      <button
        @click="generateNegativeNodes(history[currentStep].nodes.find(n => n.id === hoveredNode)!)"
        :disabled="isGenerating"
        class="block w-full text-left px-2 py-1 text-xs text-red-600 hover:bg-red-50 rounded disabled:opacity-50 disabled:cursor-not-allowed"
        title="生成消极方向"
      >
        <span v-if="isGenerating" class="inline-block w-2 h-2 border border-red-600 border-t-transparent rounded-full animate-spin mr-1"></span>
        ➖ 消极
      </button>
      <button
        @click="handleNodeDoubleClick($event, history[currentStep].nodes.find(n => n.id === hoveredNode)!)"
        class="block w-full text-left px-2 py-1 text-xs text-blue-600 hover:bg-blue-50 rounded"
        title="编辑节点"
      >
        ✏️ 编辑
      </button>
    </div>

    <!-- 增强的控制面板 -->
    <div v-if="history.length > 0" class="absolute bottom-4 left-1/2 -translate-x-1/2 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-lg shadow-lg p-3 border border-slate-200/50 dark:border-slate-700/50">
      <div class="flex items-center space-x-4">
        <!-- 生成状态指示器 -->
        <div v-if="isGenerating" class="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
          <div class="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <span class="text-xs font-medium">生成中...</span>
        </div>

        <!-- 时间线导航 -->
        <div class="flex items-center space-x-2">
          <button
            @click="goBack"
            :disabled="!canGoBack || isGenerating"
            class="w-8 h-8 bg-blue-500 text-white rounded-full disabled:opacity-30 hover:bg-blue-600 transition-colors flex items-center justify-center text-sm"
            title="上一步"
          >
            ←
          </button>

          <div class="flex space-x-1">
            <div
              v-for="(_, index) in history"
              :key="index"
              :class="[
                'w-3 h-3 rounded-full transition-all cursor-pointer relative',
                index === currentStep ? 'bg-blue-500 scale-125' : 'bg-slate-300 dark:bg-slate-600 hover:bg-slate-400'
              ]"
              @click="navigateHistory(index)"
              :title="getStepDescription(index)"
            >
              <!-- 步骤类型指示器 -->
              <div v-if="getStepType(index)" :class="[
                'absolute -top-1 -right-1 w-2 h-2 rounded-full text-xs',
                getStepType(index) === 'initial' ? 'bg-purple-500' :
                getStepType(index) === 'positive' ? 'bg-green-500' :
                getStepType(index) === 'negative' ? 'bg-red-500' : 'bg-gray-500'
              ]"></div>
            </div>
          </div>

          <button
            @click="goForward"
            :disabled="!canGoForward || isGenerating"
            class="w-8 h-8 bg-blue-500 text-white rounded-full disabled:opacity-30 hover:bg-blue-600 transition-colors flex items-center justify-center text-sm"
            title="下一步"
          >
            →
          </button>
        </div>

        <!-- 步骤信息 -->
        <div class="text-xs text-slate-600 dark:text-slate-400">
          <span>步骤 {{ currentStep + 1 }} / {{ history.length }}</span>
          <div class="text-xs opacity-75">{{ getCurrentStepInfo() }}</div>
        </div>

        <!-- 分隔线 -->
        <div class="h-6 w-px bg-slate-300 dark:bg-slate-600"></div>

        <!-- 缩放控制 -->
        <div class="flex items-center space-x-1">
          <button
            @click="zoomOut"
            class="w-7 h-7 bg-slate-500 text-white rounded text-xs hover:bg-slate-600 transition-colors flex items-center justify-center"
            title="缩小"
          >
            −
          </button>
          <button
            @click="resetZoom"
            class="w-7 h-7 bg-slate-500 text-white rounded text-xs hover:bg-slate-600 transition-colors flex items-center justify-center"
            title="重置"
          >
            ⌂
          </button>
          <button
            @click="zoomIn"
            class="w-7 h-7 bg-slate-500 text-white rounded text-xs hover:bg-slate-600 transition-colors flex items-center justify-center"
            title="放大"
          >
            +
          </button>
        </div>
      </div>
    </div>

      <div v-else class="flex items-center justify-center w-full h-full text-slate-400">
        <div class="text-center">
          <div class="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          <p class="text-sm">构建推演网络中...</p>
        </div>
      </div>
    </div>

    <!-- 右侧节点列表面板 -->
    <div v-if="showNodeList && history.length > 0" class="absolute right-0 top-0 w-80 h-full bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm border-l border-slate-200 dark:border-slate-700 shadow-lg">
      <div class="flex flex-col h-full">
        <!-- 面板头部 -->
        <div class="flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700">
          <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">节点列表</h3>
          <button
            @click="toggleNodeList"
            class="w-8 h-8 bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 rounded-full flex items-center justify-center transition-colors"
            title="隐藏面板"
          >
            ✕
          </button>
        </div>

        <!-- 节点列表 -->
        <div class="flex-1 overflow-y-auto p-4 space-y-2">
          <div
            v-for="node in getCurrentNodes"
            :key="node.id"
            :class="[
              'p-3 rounded-lg border cursor-pointer transition-all',
              selectedNode === node.id
                ? 'border-amber-400 bg-amber-50 dark:bg-amber-900/20 shadow-md'
                : 'border-slate-200 dark:border-slate-600 bg-white dark:bg-slate-700 hover:border-slate-300 dark:hover:border-slate-500 hover:shadow-sm'
            ]"
            @click="selectNodeFromList(node.id)"
          >
            <!-- 节点类型标识 -->
            <div class="flex items-start space-x-3">
              <div :class="[
                'w-3 h-3 rounded-full mt-1 flex-shrink-0',
                node.type === 'initial' ? 'bg-indigo-500' :
                node.type === 'positive' ? 'bg-green-500' :
                node.type === 'negative' ? 'bg-red-500' : 'bg-slate-500'
              ]"></div>

              <div class="flex-1 min-w-0">
                <!-- 节点标题 -->
                <h4 class="font-medium text-slate-800 dark:text-slate-200 text-sm leading-tight mb-1">
                  {{ node.title }}
                </h4>

                <!-- 节点描述 -->
                <p v-if="node.description" class="text-xs text-slate-600 dark:text-slate-400 line-clamp-2">
                  {{ node.description }}
                </p>

                <!-- 节点元信息 -->
                <div class="flex items-center space-x-2 mt-2">
                  <span :class="[
                    'px-2 py-1 rounded text-xs font-medium',
                    node.impact === 'critical' ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300' :
                    node.impact === 'high' ? 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300' :
                    node.impact === 'medium' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300' :
                    'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                  ]">
                    {{ node.impact === 'critical' ? '关键' :
                        node.impact === 'high' ? '高' :
                        node.impact === 'medium' ? '中' : '低' }}
                  </span>
                  <span class="text-xs text-slate-500 dark:text-slate-400">
                    L{{ node.level }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 面板底部统计 -->
        <div class="p-4 border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800">
          <div class="text-xs text-slate-600 dark:text-slate-400 space-y-1">
            <div class="flex justify-between">
              <span>总节点数:</span>
              <span class="font-medium">{{ getCurrentNodes.length }}</span>
            </div>
            <div class="flex justify-between">
              <span>当前步骤:</span>
              <span class="font-medium">{{ currentStep + 1 }} / {{ history.length }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 节点列表切换按钮（当面板隐藏时显示） -->
    <button
      v-if="!showNodeList && history.length > 0"
      @click="toggleNodeList"
      class="absolute top-4 right-4 w-10 h-10 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border border-slate-200 dark:border-slate-700 rounded-lg shadow-lg hover:shadow-xl transition-all flex items-center justify-center"
      title="显示节点列表"
    >
      <svg class="w-5 h-5 text-slate-600 dark:text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
      </svg>
    </button>
  </div>
</template>

<style scoped>
/* Add any specific styles for the tree graph here */
</style>
