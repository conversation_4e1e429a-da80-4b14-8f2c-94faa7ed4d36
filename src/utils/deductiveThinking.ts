import { getActiveModelConfig } from './geminiApi'
import { AnalysisNode, NodeConnection } from './systemicThinking'

export interface DeductiveAnalysisResult {
  initialTopic: string;
  positiveBranch: {
    title: string;
    description: string;
    nodes: AnalysisNode[];
    connections: NodeConnection[];
  };
  negativeBranch: {
    title: string;
    description: string;
    nodes: AnalysisNode[];
    connections: NodeConnection[];
  };
}

export class LLMDeductiveAnalyzer {
  private activeModel: any;

  constructor() {
    this.activeModel = getActiveModelConfig();
  }

  async generateDeductiveAnalysis(topic: string, thinkingPrompt: string): Promise<DeductiveAnalysisResult> {
    const prompt = this.buildPrompt(topic, thinkingPrompt);

    try {
      if (this.activeModel?.apiKey) {
        const { text } = await this.callLLMAPI(prompt);
        const parsedResult = this.parseResponse(text);
        return parsedResult;
      } else {
        // Fallback to local generation if no API key
        return this.generateLocalDeductiveAnalysis(topic);
      }
    } catch (error) {
      console.error(`LLM Deductive Analysis failed, falling back to local generation:`, error);
      return this.generateLocalDeductiveAnalysis(topic);
    }
  }

  private buildPrompt(topic: string, thinkingPrompt: string): string {
    const promptAddition = thinkingPrompt ? `\n请特别注意以下思考提示："${thinkingPrompt}"` : '';

    return `请对主题"${topic}"进行推演分析，分别从"积极方向"和"负面方向"进行深入探讨。${promptAddition}\n\n请严格按照以下JSON格式返回，确保每个分支内的节点形成一个树形结构，从根节点（level 1）开始，逐层向下推演：\n\n{\n  "initialTopic": "${topic}",\n  "positiveBranch": {\n    "title": "积极方向的总结",\n    "description": "对积极方向的详细描述",\n    "nodes": [\n      {\n        "id": "pos-node-1",\n        "title": "积极节点1",\n        "description": "积极节点1的描述",\n        "level": 1,\n        "impact": "high",\n        "modelSource": "deductive-positive",\n        "type": "positive"\n      },\n      {\n        "id": "pos-node-2",\n        "title": "积极节点2",\n        "description": "积极节点2的描述",\n        "level": 2,\n        "impact": "medium",\n        "modelSource": "deductive-positive",\n        "type": "positive"\n      }\n    ],\n    "connections": [\n      {\n        "from": "pos-node-1",\n        "to": "pos-node-2",\n        "type": "strong",\n        "description": "连接描述",\n        "strength": 0.9\n      }\n    ]\n  },\n  "negativeBranch": {\n    "title": "负面方向的总结",\n    "description": "对负面方向的详细描述",\n    "nodes": [\n      {\n        "id": "neg-node-1",\n        "title": "负面节点1",\n        "description": "负面节点1的描述",\n        "level": 1,\n        "impact": "critical",\n        "modelSource": "deductive-negative",\n        "type": "negative"\n      },\n      {\n        "id": "neg-node-2",\n        "title": "负面节点2",\n        "description": "负面节点2的描述",\n        "level": 2,\n        "impact": "high",\n        "modelSource": "deductive-negative",\n        "type": "negative"\n      }\n    ],\n    "connections": [\n      {\n        "from": "neg-node-1",\n        "to": "neg-node-2",\n        "type": "weak",\n        "description": "连接描述",\n        "strength": 0.7\n      }\n    ]\n  }\n}\n\n注意：\n- 节点ID必须唯一，且在各自的分支内以"pos-"或"neg-"开头。\n- level从1开始，表示推演的层级，根节点为1。\n- modelSource请使用"deductive-positive"和"deductive-negative"。\n- type字段必须为"positive"或"negative"。\n- 确保连接的from和to对应存在的节点ID，并且连接应体现父子关系，形成树状结构。\n- 每个分支至少生成3个节点，并确保有足够的连接来形成至少两层结构。\n`;
  }

  private async callLLMAPI(prompt: string): Promise<{ text: string, tokenCount: number }> {
    // This will use the same LLM API call logic as systemicThinking.ts
    // For simplicity, I'm reusing the geminiApi.ts logic directly here.
    // In a larger application, you might abstract this further.
    const { GoogleGenerativeAI } = await import('@google/generative-ai');
    const genAI = new GoogleGenerativeAI(this.activeModel.apiKey);
    const geminiModel = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });

    try {
      const result = await geminiModel.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      const tokenCount = (response as any).usageMetadata?.totalTokenCount || 0;
      console.log(`Gemini Deductive API Response:`, text);
      return { text, tokenCount };
    } catch (error) {
      console.error(`Gemini Deductive API Error:`, error);
      throw error; // Re-throw to be caught by generateDeductiveAnalysis
    }
  }

  private parseResponse(text: string): DeductiveAnalysisResult {
    try {
      const cleanText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const parsed = JSON.parse(cleanText);
      return parsed;
    } catch (error) {
      console.error('解析LLM推演响应失败:', error, '原始文本:', text);
      throw new Error('LLM返回的推演数据格式不正确');
    }
  }

  // 新增：用于动态扩展的LLM调用
  async callLLMForExpansion(prompt: string): Promise<{ text: string, tokenCount: number }> {
    if (this.activeModel?.apiKey) {
      return await this.callLLMAPI(prompt);
    } else {
      throw new Error('没有可用的LLM API配置');
    }
  }

  // 新增：检查模型配置的公共方法
  getModelConfig() {
    return this.activeModel ? {
      id: this.activeModel.id,
      name: this.activeModel.name,
      provider: this.activeModel.provider,
      hasApiKey: !!this.activeModel.apiKey,
      isActive: this.activeModel.isActive
    } : null
  }

  // 新增：检查是否有有效配置
  hasValidConfig(): boolean {
    return !!(this.activeModel?.apiKey)
  }

  private generateLocalDeductiveAnalysis(topic: string): DeductiveAnalysisResult {
    // 基于主题生成更智能的本地分析
    const initialNode: AnalysisNode = {
      id: 'initial-0',
      title: topic,
      description: '推演分析的起点',
      level: 0,
      impact: 'medium',
      modelSource: 'initial',
      type: 'initial'
    };

    // 生成更丰富的积极方向节点网络
    const positiveNodes: AnalysisNode[] = [
      // 第一层积极节点
      {
        id: 'pos-1',
        title: `${topic}的机遇`,
        description: `分析${topic}可能带来的积极机遇和发展空间`,
        level: 1,
        impact: 'high',
        modelSource: 'deductive-positive',
        type: 'positive'
      },
      {
        id: 'pos-2',
        title: '创新潜力',
        description: '在此领域可能产生的创新和突破',
        level: 1,
        impact: 'high',
        modelSource: 'deductive-positive',
        type: 'positive'
      },
      // 第二层积极节点
      {
        id: 'pos-3',
        title: '资源优势',
        description: '可以利用的现有资源和优势条件',
        level: 2,
        impact: 'medium',
        modelSource: 'deductive-positive',
        type: 'positive'
      },
      {
        id: 'pos-4',
        title: '长期收益',
        description: '预期的长期积极影响和收益',
        level: 2,
        impact: 'high',
        modelSource: 'deductive-positive',
        type: 'positive'
      },
      {
        id: 'pos-5',
        title: '技术突破',
        description: '可能带来的技术进步和创新',
        level: 2,
        impact: 'high',
        modelSource: 'deductive-positive',
        type: 'positive'
      },
      {
        id: 'pos-6',
        title: '社会效益',
        description: '对社会产生的积极影响和价值',
        level: 2,
        impact: 'medium',
        modelSource: 'deductive-positive',
        type: 'positive'
      },
      // 第三层积极节点
      {
        id: 'pos-7',
        title: '经济增长',
        description: '促进经济发展和增长的潜力',
        level: 3,
        impact: 'high',
        modelSource: 'deductive-positive',
        type: 'positive'
      },
      {
        id: 'pos-8',
        title: '就业机会',
        description: '创造新的就业岗位和职业发展机会',
        level: 3,
        impact: 'medium',
        modelSource: 'deductive-positive',
        type: 'positive'
      },
    ];

    // 生成更丰富的负面方向节点网络
    const negativeNodes: AnalysisNode[] = [
      // 第一层负面节点
      {
        id: 'neg-1',
        title: `${topic}的风险`,
        description: `分析${topic}可能面临的主要风险和挑战`,
        level: 1,
        impact: 'critical',
        modelSource: 'deductive-negative',
        type: 'negative'
      },
      {
        id: 'neg-2',
        title: '实施障碍',
        description: '在实施过程中可能遇到的困难和阻碍',
        level: 1,
        impact: 'high',
        modelSource: 'deductive-negative',
        type: 'negative'
      },
      // 第二层负面节点
      {
        id: 'neg-3',
        title: '资源限制',
        description: '可能遇到的资源约束和限制条件',
        level: 2,
        impact: 'high',
        modelSource: 'deductive-negative',
        type: 'negative'
      },
      {
        id: 'neg-4',
        title: '潜在后果',
        description: '如果处理不当可能产生的负面后果',
        level: 2,
        impact: 'critical',
        modelSource: 'deductive-negative',
        type: 'negative'
      },
      {
        id: 'neg-5',
        title: '技术风险',
        description: '技术实现过程中的不确定性和风险',
        level: 2,
        impact: 'high',
        modelSource: 'deductive-negative',
        type: 'negative'
      },
      {
        id: 'neg-6',
        title: '社会阻力',
        description: '来自社会各界的反对和阻力',
        level: 2,
        impact: 'medium',
        modelSource: 'deductive-negative',
        type: 'negative'
      },
      // 第三层负面节点
      {
        id: 'neg-7',
        title: '经济损失',
        description: '可能造成的经济损失和负面影响',
        level: 3,
        impact: 'critical',
        modelSource: 'deductive-negative',
        type: 'negative'
      },
      {
        id: 'neg-8',
        title: '道德争议',
        description: '可能引发的道德和伦理争议',
        level: 3,
        impact: 'medium',
        modelSource: 'deductive-negative',
        type: 'negative'
      },
    ];

    // 创建更复杂的积极方向连接网络
    const positiveConnections: NodeConnection[] = [
      // 第一层到第二层的连接
      { from: 'pos-1', to: 'pos-3', type: 'strong', description: '机遇带来资源优势' , strength: 0.9},
      { from: 'pos-1', to: 'pos-4', type: 'strong', description: '机遇产生长期收益' , strength: 0.8},
      { from: 'pos-2', to: 'pos-5', type: 'strong', description: '创新推动技术突破' , strength: 0.9},
      { from: 'pos-2', to: 'pos-6', type: 'strong', description: '创新带来社会效益' , strength: 0.7},

      // 第二层到第三层的连接
      { from: 'pos-4', to: 'pos-7', type: 'strong', description: '长期收益促进经济增长' , strength: 0.8},
      { from: 'pos-5', to: 'pos-7', type: 'strong', description: '技术突破推动经济增长' , strength: 0.7},
      { from: 'pos-6', to: 'pos-8', type: 'strong', description: '社会效益创造就业机会' , strength: 0.6},

      // 跨层级的关联连接
      { from: 'pos-1', to: 'pos-7', type: 'weak', description: '机遇直接影响经济' , strength: 0.5},
      { from: 'pos-3', to: 'pos-8', type: 'weak', description: '资源优势促进就业' , strength: 0.4},
    ];

    // 创建更复杂的负面方向连接网络
    const negativeConnections: NodeConnection[] = [
      // 第一层到第二层的连接
      { from: 'neg-1', to: 'neg-3', type: 'strong', description: '风险导致资源限制' , strength: 0.9},
      { from: 'neg-1', to: 'neg-4', type: 'strong', description: '风险产生负面后果' , strength: 0.8},
      { from: 'neg-2', to: 'neg-5', type: 'strong', description: '实施障碍带来技术风险' , strength: 0.8},
      { from: 'neg-2', to: 'neg-6', type: 'strong', description: '实施障碍引发社会阻力' , strength: 0.7},

      // 第二层到第三层的连接
      { from: 'neg-4', to: 'neg-7', type: 'strong', description: '负面后果造成经济损失' , strength: 0.9},
      { from: 'neg-5', to: 'neg-7', type: 'strong', description: '技术风险导致经济损失' , strength: 0.7},
      { from: 'neg-6', to: 'neg-8', type: 'strong', description: '社会阻力引发道德争议' , strength: 0.6},

      // 跨层级的关联连接
      { from: 'neg-1', to: 'neg-7', type: 'weak', description: '风险直接影响经济' , strength: 0.6},
      { from: 'neg-3', to: 'neg-8', type: 'weak', description: '资源限制引发争议' , strength: 0.4},
    ];

    return {
      initialTopic: topic,
      positiveBranch: {
        title: '积极方向',
        description: '对主题的积极推演',
        nodes: positiveNodes,
        connections: positiveConnections,
      },
      negativeBranch: {
        title: '负面方向',
        description: '对主题的负面推演',
        nodes: negativeNodes,
        connections: negativeConnections,
      },
    };
  }
}
