{"$schema": "../node_modules/@tauri-apps/cli/schema.json", "build": {"beforeBuildCommand": "npm run build", "beforeDevCommand": "npm run dev", "devPath": "http://localhost:5173", "distDir": "../dist", "withGlobalTauri": true}, "package": {"productName": "思维训练器", "version": "1.0.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "dialog": {"all": false, "save": true}, "fs": {"all": false, "writeFile": true, "readFile": true, "createDir": true, "removeFile": true, "exists": true, "scope": ["$APPDATA", "$APPDATA/**", "$DOCUMENT", "$DOCUMENT/**"]}, "path": {"all": true}, "os": {"all": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.thinking-trainer.app", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": [], "externalBin": [], "copyright": "", "category": "Education", "shortDescription": "思维训练器 - 提升思维能力的专业工具", "longDescription": "思维训练器是一款专业的思维能力提升工具，通过AI生成高质量的思维训练问题，帮助用户提升批判性思维、创意思维、逻辑分析等多种思维能力。", "deb": {"depends": []}, "macOS": {"frameworks": [], "minimumSystemVersion": "10.13", "exceptionDomain": "", "signingIdentity": null, "providerShortName": null, "entitlements": null}, "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "security": {"csp": null}, "updater": {"active": false}, "windows": [{"fullscreen": false, "resizable": true, "title": "思维训练器", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "center": true, "decorations": true, "alwaysOnTop": false, "skipTaskbar": false, "theme": "Light"}]}}