[package]
name = "thinking-trainer"
version = "1.0.0"
description = "思维训练器 - 提升思维能力的专业工具"
authors = ["you"]
license = ""
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5", features = [] }

[dependencies]
tauri = { version = "1.5", features = ["api-all", "shell-open", "dialog-save", "fs-write-file", "fs-read-file", "fs-create-dir", "fs-remove-file", "fs-exists", "path-all", "os-all"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]